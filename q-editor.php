<?php
/**
 * Plugin Name: Q-Editor
 * Description: Adds ACE Editor with Live Preview (processing shortcodes) to the Formidable Views editor.
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL2
 * Text Domain: q-editor
 */

if (!defined('ABSPATH'))
    exit;

/**
 * Register the Q-View custom post type.
 */
function qeditor_register_qview_post_type()
{
    $labels = array(
        'name' => _x('Q-Views', 'Post type general name', 'q-editor'),
        'singular_name' => _x('Q-View', 'Post type singular name', 'q-editor'),
        'menu_name' => _x('Q-Views', 'Admin Menu text', 'q-editor'),
        'name_admin_bar' => _x('Q-View', 'Add New on Toolbar', 'q-editor'),
        'add_new' => __('Add New', 'q-editor'),
        'add_new_item' => __('Add New Q-View', 'q-editor'),
        'new_item' => __('New Q-View', 'q-editor'),
        'edit_item' => __('Edit Q-View', 'q-editor'),
        'view_item' => __('View Q-View', 'q-editor'),
        'all_items' => __('All Q-Views', 'q-editor'),
        'search_items' => __('Search Q-Views', 'q-editor'),
        'parent_item_colon' => __('Parent Q-Views:', 'q-editor'),
        'not_found' => __('No Q-Views found.', 'q-editor'),
        'not_found_in_trash' => __('No Q-Views found in Trash.', 'q-editor'),
        'featured_image' => _x('Q-View Featured Image', 'Overrides the "Featured Image" phrase', 'q-editor'),
        'set_featured_image' => _x('Set featured image', 'Overrides the "Set featured image" phrase', 'q-editor'),
        'remove_featured_image' => _x('Remove featured image', 'Overrides the "Remove featured image" phrase', 'q-editor'),
        'use_featured_image' => _x('Use as featured image', 'Overrides the "Use as featured image" phrase', 'q-editor'),
        'archives' => _x('Q-View archives', 'The post type archive label', 'q-editor'),
        'insert_into_item' => _x('Insert into Q-View', 'Overrides the "Insert into post" phrase', 'q-editor'),
        'uploaded_to_this_item' => _x('Uploaded to this Q-View', 'Overrides the "Uploaded to this post" phrase', 'q-editor'),
        'filter_items_list' => _x('Filter Q-Views list', 'Screen reader text for the filter links', 'q-editor'),
        'items_list_navigation' => _x('Q-Views list navigation', 'Screen reader text for the pagination', 'q-editor'),
        'items_list' => _x('Q-Views list', 'Screen reader text for the items list', 'q-editor'),
    );

    $args = array(
        'labels' => $labels,
        'public' => false,
        'publicly_queryable' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'q-view'),
        'capability_type' => 'post',
        'has_archive' => false,
        'hierarchical' => false,
        'menu_position' => null,
        'menu_icon' => 'dashicons-visibility',
        'supports' => array('title', 'author', 'custom-fields'),
        'show_in_rest' => false, // Disable Gutenberg
        'taxonomies' => array('q-view-category'), // Add support for categories
    );

    register_post_type('q-view', $args);
}
add_action('init', 'qeditor_register_qview_post_type');

/**
 * Register the Q-View Category taxonomy.
 */
function qeditor_register_qview_category_taxonomy()
{
    $labels = array(
        'name' => _x('Q-View Categories', 'taxonomy general name', 'q-editor'),
        'singular_name' => _x('Q-View Category', 'taxonomy singular name', 'q-editor'),
        'search_items' => __('Search Categories', 'q-editor'),
        'all_items' => __('All Categories', 'q-editor'),
        'parent_item' => __('Parent Category', 'q-editor'),
        'parent_item_colon' => __('Parent Category:', 'q-editor'),
        'edit_item' => __('Edit Category', 'q-editor'),
        'update_item' => __('Update Category', 'q-editor'),
        'add_new_item' => __('Add New Category', 'q-editor'),
        'new_item_name' => __('New Category Name', 'q-editor'),
        'menu_name' => __('Categories', 'q-editor'),
    );

    $args = array(
        'hierarchical' => true,
        'labels' => $labels,
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'q-view-category'),
        'show_in_rest' => false,
        'public' => false,
        'publicly_queryable' => false,
    );

    register_taxonomy('q-view-category', array('q-view'), $args);
}
add_action('init', 'qeditor_register_qview_category_taxonomy');

/**
 * Add meta boxes for Q-View post type.
 */
function qeditor_add_qview_meta_boxes()
{
    add_meta_box(
        'qview-settings',
        __('Q-View Settings', 'q-editor'),
        'qeditor_qview_settings_callback',
        'q-view',
        'side',
        'default'
    );
}
add_action('add_meta_boxes', 'qeditor_add_qview_meta_boxes');

/**
 * Meta box callback for Q-View settings.
 */
function qeditor_qview_settings_callback($post)
{
    wp_nonce_field('qeditor_qview_meta_box', 'qeditor_qview_meta_box_nonce');

    $associated_form = get_post_meta($post->ID, '_qview_associated_form', true);
    $view_type = get_post_meta($post->ID, '_qview_type', true);

    echo '<p>';
    echo '<label for="qview_type"><strong>' . __('View Type:', 'q-editor') . '</strong></label><br>';
    echo '<select id="qview_type" name="qview_type" style="width: 100%;">';
    echo '<option value="general"' . selected($view_type, 'general', false) . '>' . __('General View', 'q-editor') . '</option>';
    echo '<option value="form-specific"' . selected($view_type, 'form-specific', false) . '>' . __('Form-Specific View', 'q-editor') . '</option>';
    echo '</select>';
    echo '</p>';

    // Category selection
    echo '<p>';
    echo '<label for="qview_category"><strong>' . __('Category:', 'q-editor') . '</strong></label><br>';
    $categories = get_terms(array(
        'taxonomy' => 'q-view-category',
        'hide_empty' => false,
    ));

    $selected_categories = wp_get_post_terms($post->ID, 'q-view-category', array('fields' => 'ids'));
    $selected_category = !empty($selected_categories) ? $selected_categories[0] : '';

    echo '<select id="qview_category" name="qview_category" style="width: 100%;">';
    echo '<option value="">' . __('No Category', 'q-editor') . '</option>';

    if (!is_wp_error($categories) && !empty($categories)) {
        foreach ($categories as $category) {
            echo '<option value="' . esc_attr($category->term_id) . '"' . selected($selected_category, $category->term_id, false) . '>';
            echo esc_html($category->name);
            echo '</option>';
        }
    }
    echo '</select>';
    echo '</p>';

    echo '<p>';
    echo '<label for="qview_associated_form"><strong>' . __('Associated Form (optional):', 'q-editor') . '</strong></label><br>';
    echo '<select id="qview_associated_form" name="qview_associated_form" style="width: 100%;">';
    echo '<option value="">' . __('None', 'q-editor') . '</option>';

    if (class_exists('FrmForm')) {
        $forms = FrmForm::get_published_forms();
        foreach ($forms as $form) {
            echo '<option value="' . esc_attr($form->id) . '"' . selected($associated_form, $form->id, false) . '>';
            echo esc_html($form->name . ' (ID: ' . $form->id . ')');
            echo '</option>';
        }
    }

    echo '</select>';
    echo '</p>';

    echo '<p class="description">';
    echo __('Form-specific views will only appear when editing views for the associated form.', 'q-editor');
    echo '</p>';
}

/**
 * Save Q-View meta box data.
 */
function qeditor_save_qview_meta_box($post_id)
{
    if (!isset($_POST['qeditor_qview_meta_box_nonce'])) {
        return;
    }

    if (!wp_verify_nonce($_POST['qeditor_qview_meta_box_nonce'], 'qeditor_qview_meta_box')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (isset($_POST['post_type']) && 'q-view' == $_POST['post_type']) {
        if (!current_user_can('edit_page', $post_id)) {
            return;
        }
    } else {
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
    }

    if (isset($_POST['qview_type'])) {
        update_post_meta($post_id, '_qview_type', sanitize_text_field($_POST['qview_type']));
    }

    if (isset($_POST['qview_associated_form'])) {
        update_post_meta($post_id, '_qview_associated_form', sanitize_text_field($_POST['qview_associated_form']));
    }

    // Handle category assignment
    if (isset($_POST['qview_category'])) {
        $category_id = intval($_POST['qview_category']);
        if ($category_id > 0) {
            wp_set_post_terms($post_id, array($category_id), 'q-view-category');
        } else {
            // Remove all categories if "No Category" is selected
            wp_set_post_terms($post_id, array(), 'q-view-category');
        }
    }
}
add_action('save_post', 'qeditor_save_qview_meta_box');

/**
 * Remove default editor for q-view post type and add ACE editor.
 */
function qeditor_remove_default_editor_for_qview()
{
    $screen = get_current_screen();
    if ($screen && $screen->post_type === 'q-view') {
        remove_post_type_support('q-view', 'editor');
    }
}
add_action('admin_head', 'qeditor_remove_default_editor_for_qview');

/**
 * Add ACE editor meta box for q-view post type.
 */
function qeditor_add_ace_editor_meta_box()
{
    add_meta_box(
        'qview-ace-editor',
        __('Q-View Content (ACE Editor)', 'q-editor'),
        'qeditor_ace_editor_meta_box_callback',
        'q-view',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'qeditor_add_ace_editor_meta_box');

/**
 * ACE editor meta box callback.
 */
function qeditor_ace_editor_meta_box_callback($post)
{
    wp_nonce_field('qeditor_ace_editor_meta_box', 'qeditor_ace_editor_meta_box_nonce');

    $content = $post->post_content;

    // Create the toolbar for Q-View editor
    echo '<div id="qview-ace-toolbar" style="background: #f0f0f1; border: 1px solid #ddd; border-bottom: none; padding: 10px; display: flex; align-items: center; gap: 10px; flex-wrap: wrap;">
        <div class="qview-toolbar-group" style="display: flex; align-items: center; gap: 5px; border-right: 1px solid #c3c4c7; padding-right: 10px;">
            <button type="button" id="qview-beautify">Beautify Code</button>
            <button type="button" id="qview-toggle-preview">Toggle Preview</button>
        </div>
        <div class="qview-toolbar-group" style="display: flex; align-items: center; gap: 5px; border-right: 1px solid #c3c4c7; padding-right: 10px;">
            <label for="qview-preview-form-select" style="font-size: 12px; font-weight: 600; color: #555;">Form:</label>
            <select id="qview-preview-form-select" style="min-width: 120px;">
                <option value="">Select Form...</option>
            </select>
            <label for="qview-preview-entry-select" style="font-size: 12px; font-weight: 600; color: #555;">Entry:</label>
            <select id="qview-preview-entry-select" style="min-width: 120px;">
                <option value="">Select Entry...</option>
                <option value="sample">Use Sample Data</option>
            </select>
        </div>
        <div class="qview-toolbar-group" style="display: flex; align-items: center; gap: 5px;">
            <button type="button" id="qview-refresh-preview">Refresh Preview</button>
            <span id="qview-preview-context-info" style="font-size: 11px; color: #666; font-style: italic;"></span>
        </div>
    </div>';

    // Create the editor and preview container
    echo '<div id="qview-editor-preview-container" style="display: flex; width: 100%; gap: 0; border: 1px solid #ddd;">
        <div id="qview-ace-editor-container" style="flex: 2; height: 500px; border: none; border-right: 1px solid #c3c4c7; background: #1e1e1e;"></div>
        <div id="qview-live-preview" style="flex: 1; min-width: 300px; height: 500px; border: none; background: #fff; overflow: auto; padding: 10px; display: none;"></div>
    </div>';

    echo '<textarea id="qview-content-textarea" name="content" style="display: none;">' . esc_textarea($content) . '</textarea>';

    // Add the ACE editor initialization script
    ?>
    <script>
        jQuery(document).ready(function ($) {
            // Define AJAX variables for Q-View editor
            var qeditor_ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
            var qeditor_nonce = '<?php echo wp_create_nonce('qeditor_nonce'); ?>';

            // Load ACE editor if not already loaded
            if (typeof ace === 'undefined') {
                var aceScript = document.createElement('script');
                aceScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/ace/1.23.1/ace.js';
                aceScript.onload = function () {
                    initQViewAceEditor();
                };
                document.head.appendChild(aceScript);
            } else {
                initQViewAceEditor();
            }

            function initQViewAceEditor() {
                var editor = ace.edit('qview-ace-editor-container');
                var textarea = $('#qview-content-textarea');
                var previewPane = $('#qview-live-preview');
                var previewVisible = false;
                var selectedFormId = '';
                var selectedEntryId = '';

                // Configure ACE editor
                editor.setOptions({
                    fontSize: '14px',
                    wrap: true,
                    showPrintMargin: false,
                    enableBasicAutocompletion: true,
                    enableSnippets: true,
                    enableLiveAutocompletion: true
                });

                // Set theme and mode
                editor.setTheme('ace/theme/monokai');
                editor.session.setMode('ace/mode/html');

                // Set initial content
                editor.setValue(textarea.val(), -1);

                // Sync editor content with textarea
                editor.getSession().on('change', function () {
                    textarea.val(editor.getValue());
                    updatePreview();
                });

                // Handle form submission
                $('form#post').on('submit', function () {
                    textarea.val(editor.getValue());
                });

                // Add status bar
                var statusBar = $('<div id="qview-ace-status" style="background: #222; color: #aaa; font-size: 12px; padding: 5px; border: 1px solid #ddd; border-top: none;"></div>');
                $('#qview-editor-preview-container').after(statusBar);

                editor.getSession().selection.on('changeCursor', function () {
                    var pos = editor.getCursorPosition();
                    statusBar.text('Line: ' + (pos.row + 1) + ', Column: ' + (pos.column + 1));
                });

                // Load forms on initialization
                loadForms();

                // Event handlers for toolbar buttons
                $('#qview-beautify').on('click', function () {
                    // Load beautify library if not loaded
                    if (typeof html_beautify === 'undefined') {
                        var beautifyScript = document.createElement('script');
                        beautifyScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.7/beautify-html.min.js';
                        beautifyScript.onload = function () {
                            if (typeof html_beautify !== 'undefined') {
                                editor.setValue(html_beautify(editor.getValue()), -1);
                                updatePreview();
                            }
                        };
                        document.head.appendChild(beautifyScript);
                    } else {
                        editor.setValue(html_beautify(editor.getValue()), -1);
                        updatePreview();
                    }
                });

                $('#qview-toggle-preview').on('click', function () {
                    previewVisible = !previewVisible;
                    if (previewVisible) {
                        previewPane.show();
                        updatePreview();
                        $(this).text('Hide Preview');
                    } else {
                        previewPane.hide();
                        $(this).text('Toggle Preview');
                    }
                });

                $('#qview-refresh-preview').on('click', function () {
                    updatePreview();
                });

                // Event handlers for form/entry selection
                $('#qview-preview-form-select').on('change', function () {
                    selectedFormId = $(this).val();
                    selectedEntryId = '';
                    $('#qview-preview-entry-select').val('');
                    loadEntries(selectedFormId);
                    updatePreview();
                });

                $('#qview-preview-entry-select').on('change', function () {
                    selectedEntryId = $(this).val();
                    updatePreview();
                });

                // Load forms function
                function loadForms() {
                    $.post(qeditor_ajaxurl, {
                        action: 'qeditor_get_forms',
                        nonce: qeditor_nonce
                    }, function (response) {
                        if (response.success && response.data) {
                            var formSelect = $('#qview-preview-form-select');
                            formSelect.empty().append('<option value="">Select Form...</option>');
                            $.each(response.data, function (id, name) {
                                formSelect.append('<option value="' + id + '">' + name + '</option>');
                            });
                        }
                    }).fail(function (xhr, status, error) {
                        console.log('Failed to load forms:', error);
                    });
                }

                // Load entries for selected form
                function loadEntries(formId) {
                    if (!formId) {
                        $('#qview-preview-entry-select').empty().append('<option value="">Select Entry...</option><option value="sample">Use Sample Data</option>');
                        return;
                    }

                    $.post(qeditor_ajaxurl, {
                        action: 'qeditor_get_entries',
                        form_id: formId,
                        nonce: qeditor_nonce
                    }, function (response) {
                        var entrySelect = $('#qview-preview-entry-select');
                        entrySelect.empty().append('<option value="">Select Entry...</option><option value="sample">Use Sample Data</option>');

                        if (response.success && response.data) {
                            $.each(response.data, function (id, title) {
                                entrySelect.append('<option value="' + id + '">' + title + '</option>');
                            });
                        }
                    }).fail(function (xhr, status, error) {
                        console.log('Failed to load entries for form ' + formId + ':', error);
                    });
                }

                // Update preview function
                function updatePreview() {
                    if (previewVisible) {
                        var code = editor.getValue();
                        previewPane.html('<em>Loading preview...</em>');

                        // Update context info
                        var contextInfo = '';
                        if (selectedFormId && selectedEntryId) {
                            if (selectedEntryId === 'sample') {
                                contextInfo = 'Using sample data for Form #' + selectedFormId;
                            } else {
                                contextInfo = 'Using Entry #' + selectedEntryId + ' from Form #' + selectedFormId;
                            }
                        } else {
                            contextInfo = 'No entry context - shortcodes may show empty values';
                        }
                        $('#qview-preview-context-info').text(contextInfo);

                        $.post(qeditor_ajaxurl, {
                            action: 'qeditor_render_preview',
                            content: code,
                            form_id: selectedFormId,
                            entry_id: selectedEntryId,
                            nonce: qeditor_nonce
                        }, function (response) {
                            if (response.success) {
                                previewPane.html(response.data);
                            } else {
                                previewPane.html('<em>Error rendering preview: ' + (response.data || 'Unknown error') + '</em>');
                            }
                        }).fail(function (xhr, status, error) {
                            console.log('Failed to load preview:', error);
                            previewPane.html('<em>Failed to load preview - check console for errors</em>');
                        });
                    }
                }
            }
        });
    </script>
    <style>
        /* Q-View Editor Styling */
        #qview-ace-toolbar button {
            background: #0073aa;
            color: white;
            border: none;
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 3px;
            font-size: 13px;
            margin-right: 5px;
        }

        #qview-ace-toolbar button:hover {
            background: #005a87;
        }

        #qview-ace-toolbar select {
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 13px;
        }

        #qview-ace-toolbar label {
            font-size: 12px;
            font-weight: 600;
            color: #555;
            margin-right: 5px;
        }

        .qview-toolbar-group {
            display: flex;
            align-items: center;
            gap: 5px;
            border-right: 1px solid #c3c4c7;
            padding-right: 10px;
        }

        .qview-toolbar-group:last-child {
            border-right: none;
            padding-right: 0;
        }

        #qview-preview-context-info {
            font-size: 11px;
            color: #666;
            font-style: italic;
            margin-left: 10px;
        }

        #qview-live-preview {
            border-left: 1px solid #c3c4c7;
            background: #fff;
            overflow: auto;
            padding: 15px;
        }

        #qview-live-preview:empty::before {
            content: "Click 'Toggle Preview' to see live preview of your Q-View template";
            color: #666;
            font-style: italic;
            display: block;
            text-align: center;
            padding: 50px 20px;
        }

        #qview-ace-status {
            background: #222;
            color: #aaa;
            font-size: 12px;
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-top: none;
        }

        /* Responsive adjustments */
        @media (max-width: 1200px) {
            #qview-editor-preview-container {
                flex-direction: column;
            }

            #qview-live-preview {
                border-left: none;
                border-top: 1px solid #c3c4c7;
                min-height: 300px;
            }
        }
    </style>
    <?php
}

/**
 * Save ACE editor content for q-view posts.
 */
function qeditor_save_ace_editor_content($post_id)
{
    if (!isset($_POST['qeditor_ace_editor_meta_box_nonce'])) {
        return;
    }

    if (!wp_verify_nonce($_POST['qeditor_ace_editor_meta_box_nonce'], 'qeditor_ace_editor_meta_box')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    $post = get_post($post_id);
    if (!$post || $post->post_type !== 'q-view') {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    if (isset($_POST['content'])) {
        // Remove the action to prevent infinite loop
        remove_action('save_post', 'qeditor_save_ace_editor_content');

        // Update post content
        wp_update_post(array(
            'ID' => $post_id,
            'post_content' => wp_unslash($_POST['content'])
        ));

        // Re-add the action
        add_action('save_post', 'qeditor_save_ace_editor_content');
    }
}
add_action('save_post', 'qeditor_save_ace_editor_content');

/**
 * Enqueue Q-Editor scripts and styles in the admin.
 */
function qeditor_enqueue_admin_assets($hook)
{
    // Only load on admin screens where we need the editor
    if (is_admin()) {
        // Add ajaxurl global and nonce for security
        wp_localize_script('jquery', 'qeditor_ajax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('qeditor_nonce')
        ));
    }
}
add_action('admin_enqueue_scripts', 'qeditor_enqueue_admin_assets');

/**
 * Add ACE Editor UI and functionality.
 */
function qeditor_add_ace_editor()
{
    // Debug: Let's see what screen we're on
    $screen = get_current_screen();

    // For debugging - let's be more permissive and add some logging
    $debug_info = array(
        'screen_base' => $screen ? $screen->base : 'no_screen',
        'screen_id' => $screen ? $screen->id : 'no_screen',
        'get_params' => $_GET,
        'user_can_edit' => current_user_can('edit_posts')
    );

    // Add debug info to JavaScript console
    echo '<script>console.log("Q-Editor Debug Info:", ' . json_encode($debug_info) . ');</script>';

    // More permissive check - only exclude if we're definitely not on an editor page
    if ($screen && !current_user_can('edit_posts')) {
        return;
    }
    ?>
    <style>
        /* Q-Editor Tab Styling */
        .switch-qeditor {
            background: #f0f0f1;
            border: 1px solid #c3c4c7;
            color: #50575e;
            cursor: pointer;
            font-size: 13px;
            line-height: 1;
            margin: 0;
            outline: 0;
            padding: 3px 8px 4px;
            position: relative;
            text-decoration: none;
            user-select: none;
        }

        .switch-qeditor:hover {
            background: #fff;
            color: #2c3338;
        }

        .switch-qeditor.active {
            background: #fff;
            border-bottom-color: #fff;
            color: #2c3338;
            margin-bottom: -1px;
            padding-bottom: 5px;
        }

        /* Q-Editor Container - positioned to replace the editor content area */
        #ace-container-parent {
            display: none;
            width: 100%;
            gap: 10px;
            flex-wrap: nowrap;
            position: relative;
            background: #fff;
            border: 1px solid #c3c4c7;
            border-top: none;
            /* Connect with tabs */
        }

        #ace-editor-container {
            flex: 2;
            height: 500px;
            border: none;
            border-right: 1px solid #c3c4c7;
            background: #1e1e1e;
        }

        #ace-live-preview {
            flex: 1;
            min-width: 300px;
            border: none;
            background: #fff;
            overflow: auto;
            padding: 10px;
        }

        #ace-toolbar {
            background: #f0f0f1;
            border: none;
            border-bottom: 1px solid #c3c4c7;
            padding: 10px;
            display: none;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        #ace-toolbar select,
        #ace-toolbar button {
            margin-right: 0;
        }

        #ace-toolbar .toolbar-group {
            display: flex;
            align-items: center;
            gap: 5px;
            border-right: 1px solid #c3c4c7;
            padding-right: 10px;
        }

        #ace-toolbar .toolbar-group:last-child {
            border-right: none;
            padding-right: 0;
        }

        #ace-toolbar label {
            font-size: 12px;
            font-weight: 600;
            color: #555;
        }

        #ace-toolbar select {
            min-width: 120px;
        }

        #preview-context-info {
            font-size: 11px;
            color: #666;
            font-style: italic;
        }

        #ace-statusbar {
            background: #222;
            color: #aaa;
            font-size: 12px;
            padding: 5px;
            display: none;
            border: none;
            border-top: 1px solid #c3c4c7;
        }

        /* Hide the original editor content when Q-Editor is active */
        .qeditor-active #wp-content-editor-container {
            display: none !important;
        }

        /* Ensure Q-Editor content appears in the right place */
        .qeditor-active #ace-container-parent {
            display: flex !important;
        }

        .qeditor-active #ace-toolbar {
            display: flex !important;
        }

        .qeditor-active #ace-statusbar {
            display: block !important;
        }
    </style>


    <script>
        // Wrap everything in an IIFE to prevent conflicts
        (function () {
            // Define ajaxurl globally for this plugin
            var qeditor_ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
            var qeditor_nonce = '<?php echo wp_create_nonce('qeditor_nonce'); ?>';

            console.log('Q-Editor: Initializing with AJAX URL:', qeditor_ajaxurl);

            jQuery(document).ready(function ($) {
                // Check if we're on the right page
                if (!$('.wp-editor-tabs').length) {
                    console.log('Q-Editor: No editor tabs found, skipping initialization');
                    return;
                }

                console.log('Q-Editor: Editor tabs found, proceeding with initialization');

                // Ensure ajaxurl is available as fallback
                if (typeof ajaxurl === 'undefined') {
                    window.ajaxurl = qeditor_ajaxurl;
                }

                if (typeof ace === 'undefined') {
                    var aceScript = document.createElement('script');
                    aceScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/ace/1.23.1/ace.js';
                    document.head.appendChild(aceScript);
                }

                var aceTab = $('<button>', {
                    type: 'button',
                    id: 'content-qeditor',
                    class: 'wp-switch-editor switch-qeditor',
                    text: 'Q-Editor'
                });

                console.log('Q-Editor: Creating tab button:', aceTab);
                console.log('Q-Editor: Found editor tabs container:', $('.wp-editor-tabs'));
                console.log('Q-Editor: Editor tabs container length:', $('.wp-editor-tabs').length);

                $('.wp-editor-tabs').append(aceTab);

                console.log('Q-Editor: Tab appended. Current tabs:', $('.wp-editor-tabs').children());

                // Create Q-Editor container structure - position it to replace the editor content
                var aceToolbar = $('<div>', {
                    id: 'ace-toolbar',
                    html: `
                <div class="toolbar-group">
                    <button id="beautify-code">Beautify</button>
                    <button id="live-preview">Toggle Preview</button>
                </div>
                <div class="toolbar-group">
                    <label for="preview-form-select">Form:</label>
                    <select id="preview-form-select">
                        <option value="">Select Form...</option>
                    </select>
                    <label for="preview-entry-select">Entry:</label>
                    <select id="preview-entry-select">
                        <option value="">Select Entry...</option>
                        <option value="sample">Use Sample Data</option>
                    </select>
                </div>
                <div class="toolbar-group">
                    <label for="qview-select">Q-View:</label>
                    <select id="qview-select">
                        <option value="">Select Q-View...</option>
                    </select>
                    <button id="load-qview">Load</button>
                    <button id="save-qview">Save as Q-View</button>
                </div>
                <div class="toolbar-group">
                    <button id="refresh-preview">Refresh Preview</button>
                    <span id="preview-context-info"></span>
                </div>
            `
                }).insertAfter('#wp-content-editor-container');

                var aceContainerParent = $('<div>', { id: 'ace-container-parent', style: 'display: flex;' }).insertAfter(aceToolbar);
                var aceContainer = $('<div>', { id: 'ace-editor-container' }).appendTo(aceContainerParent);
                var previewPane = $('<div>', { id: 'ace-live-preview' }).appendTo(aceContainerParent);

                var statusBar = $('<div>', { id: 'ace-statusbar' }).insertAfter(aceContainerParent);

                var textarea = $('#content');
                var aceEditor;
                var previewVisible = false;
                var selectedFormId = '';
                var selectedEntryId = '';

                // Load forms on initialization
                function loadForms() {
                    $.post(qeditor_ajaxurl, {
                        action: 'qeditor_get_forms',
                        nonce: qeditor_nonce
                    }, function (response) {
                        if (response.success && response.data) {
                            var formSelect = $('#preview-form-select');
                            formSelect.empty().append('<option value="">Select Form...</option>');
                            $.each(response.data, function (id, name) {
                                formSelect.append('<option value="' + id + '">' + name + '</option>');
                            });
                        }
                    }).fail(function (xhr, status, error) {
                        console.log('Failed to load forms:', error);
                        console.log('Response:', xhr.responseText);
                    });
                }

                // Load entries for selected form
                function loadEntries(formId) {
                    if (!formId) {
                        $('#preview-entry-select').empty().append('<option value="">Select Entry...</option><option value="sample">Use Sample Data</option>');
                        return;
                    }

                    $.post(qeditor_ajaxurl, {
                        action: 'qeditor_get_entries',
                        form_id: formId,
                        nonce: qeditor_nonce
                    }, function (response) {
                        var entrySelect = $('#preview-entry-select');
                        entrySelect.empty().append('<option value="">Select Entry...</option><option value="sample">Use Sample Data</option>');

                        if (response.success && response.data) {
                            $.each(response.data, function (id, title) {
                                entrySelect.append('<option value="' + id + '">' + title + '</option>');
                            });
                        }
                    }).fail(function (xhr, status, error) {
                        console.log('Failed to load entries for form ' + formId + ':', error);
                        console.log('Response:', xhr.responseText);
                    });
                }

                function initAceEditor() {
                    if (!aceEditor) {
                        aceEditor = ace.edit('ace-editor-container');
                        aceEditor.setOptions({
                            fontSize: '14px',
                            wrap: true, // Always wrapped
                            showPrintMargin: false
                        });

                        // For autocompletion:
                        ace.config.loadModule('ace/ext/language_tools', function () {
                            aceEditor.setOptions({
                                enableBasicAutocompletion: true,
                                enableSnippets: true,
                                enableLiveAutocompletion: true
                            });
                        });

                        // Set fixed HTML mode
                        aceEditor.setTheme('ace/theme/monokai');
                        aceEditor.session.setMode('ace/mode/html');

                        aceEditor.getSession().selection.on('changeCursor', function () {
                            var pos = aceEditor.getCursorPosition();
                            statusBar.text('Line: ' + (pos.row + 1) + ', Column: ' + (pos.column + 1));
                        });
                    }
                    aceEditor.setValue(textarea.val(), -1);
                    aceEditor.focus();
                    updatePreview();
                }

                $('#beautify-code').on('click', function () {
                    var code = aceEditor.getValue();
                    if (window.html_beautify) {
                        aceEditor.setValue(html_beautify(code), -1);
                    }
                    updatePreview();
                });

                $('#live-preview').on('click', function () {
                    previewVisible = !previewVisible;
                    if (previewVisible) {
                        previewPane.show();
                        updatePreview();
                    } else {
                        previewPane.hide();
                    }
                });

                function updatePreview() {
                    if (previewVisible) {
                        var code = aceEditor.getValue();
                        previewPane.html('<em>Loading preview...</em>');

                        // Update context info
                        var contextInfo = '';
                        if (selectedFormId && selectedEntryId) {
                            if (selectedEntryId === 'sample') {
                                contextInfo = 'Using sample data for Form #' + selectedFormId;
                            } else {
                                contextInfo = 'Using Entry #' + selectedEntryId + ' from Form #' + selectedFormId;
                            }
                        } else {
                            contextInfo = 'No entry context - shortcodes may show empty values';
                        }
                        $('#preview-context-info').text(contextInfo);

                        $.post(qeditor_ajaxurl, {
                            action: 'qeditor_render_preview',
                            content: code,
                            form_id: selectedFormId,
                            entry_id: selectedEntryId,
                            nonce: qeditor_nonce
                        }, function (response) {
                            if (response.success) {
                                previewPane.html(response.data);
                            } else {
                                previewPane.html('<em>Error rendering preview: ' + (response.data || 'Unknown error') + '</em>');
                            }
                        }).fail(function (xhr, status, error) {
                            console.log('Failed to load preview:', error);
                            console.log('Response:', xhr.responseText);
                            previewPane.html('<em>Failed to load preview - check console for errors</em>');
                        });
                    }
                }

                $('.wp-switch-editor').on('click', function () {
                    var activeTabId = $(this).attr('id');

                    if ($('#content-qeditor').hasClass('active') && aceEditor) {
                        textarea.val(aceEditor.getValue());
                    }

                    $('.wp-switch-editor').removeClass('active').removeAttr('aria-pressed');
                    $(this).addClass('active').attr('aria-pressed', 'true');

                    if (activeTabId === 'content-html' || activeTabId === 'content-tmce') {
                        // Show standard editor
                        $('body').removeClass('qeditor-active');
                        textarea.show();
                        aceContainerParent.hide();
                        aceToolbar.hide();
                        statusBar.hide();
                        $('#wp-content-editor-container').show();
                    } else if (activeTabId === 'content-qeditor') {
                        // Show Q-Editor
                        $('body').addClass('qeditor-active');
                        textarea.hide();
                        $('#wp-content-editor-container').hide();
                        aceContainerParent.show();
                        aceToolbar.show();
                        statusBar.show();
                        initAceEditor();
                    }
                });

                $('form').on('submit', function () {
                    if ($('#content-qeditor').attr('aria-pressed') === 'true' && aceEditor) {
                        textarea.val(aceEditor.getValue());
                    }
                });

                // Event handlers for form/entry selection
                $('#preview-form-select').on('change', function () {
                    selectedFormId = $(this).val();
                    selectedEntryId = '';
                    $('#preview-entry-select').val('');
                    loadEntries(selectedFormId);
                    loadQViews(); // Reload Q-Views when form changes
                    updatePreview();
                });

                $('#preview-entry-select').on('change', function () {
                    selectedEntryId = $(this).val();
                    updatePreview();
                });

                $('#refresh-preview').on('click', function () {
                    updatePreview();
                });

                // Event handlers for Q-View functionality
                $('#load-qview').on('click', function () {
                    var qviewId = $('#qview-select').val();
                    if (qviewId) {
                        loadQViewContent(qviewId);
                    } else {
                        alert('Please select a Q-View to load.');
                    }
                });

                $('#save-qview').on('click', function () {
                    saveAsQView();
                });

                $('#qview-select').on('change', function () {
                    // Optional: Auto-load when selection changes
                    // var qviewId = $(this).val();
                    // if (qviewId) {
                    //     loadQViewContent(qviewId);
                    // }
                });

                // Load Q-Views for the current form context
                function loadQViews() {
                    $.post(qeditor_ajaxurl, {
                        action: 'qeditor_get_qviews',
                        form_id: selectedFormId,
                        nonce: qeditor_nonce
                    }, function (response) {
                        if (response.success && response.data) {
                            var qviewSelect = $('#qview-select');
                            qviewSelect.empty().append('<option value="">Select Q-View...</option>');
                            $.each(response.data, function (id, title) {
                                qviewSelect.append('<option value="' + id + '">' + title + '</option>');
                            });
                        }
                    }).fail(function (xhr, status, error) {
                        console.log('Failed to load Q-Views:', error);
                        console.log('Response:', xhr.responseText);
                    });
                }

                // Load Q-View content into editor
                function loadQViewContent(qviewId) {
                    if (!qviewId) return;

                    $.post(qeditor_ajaxurl, {
                        action: 'qeditor_get_qview_content',
                        qview_id: qviewId,
                        nonce: qeditor_nonce
                    }, function (response) {
                        if (response.success && response.data) {
                            if (aceEditor) {
                                aceEditor.setValue(response.data, -1);
                                textarea.val(response.data);
                                updatePreview();
                            }
                        } else {
                            alert('Failed to load Q-View: ' + (response.data || 'Unknown error'));
                        }
                    }).fail(function (xhr, status, error) {
                        console.log('Failed to load Q-View content:', error);
                        console.log('Response:', xhr.responseText);
                        alert('Failed to load Q-View - check console for errors');
                    });
                }

                // Save current content as Q-View
                function saveAsQView() {
                    var title = prompt('Enter a title for this Q-View:');
                    if (!title) return;

                    var content = aceEditor ? aceEditor.getValue() : textarea.val();

                    $.post(qeditor_ajaxurl, {
                        action: 'qeditor_save_qview',
                        title: title,
                        content: content,
                        form_id: selectedFormId,
                        nonce: qeditor_nonce
                    }, function (response) {
                        if (response.success) {
                            alert('Q-View saved successfully!');
                            loadQViews(); // Refresh the list
                        } else {
                            alert('Failed to save Q-View: ' + (response.data || 'Unknown error'));
                        }
                    }).fail(function (xhr, status, error) {
                        console.log('Failed to save Q - View:', error);
                        console.log('Response:', xhr.responseText);
                        alert('Failed to save Q - View - check console for errors ');
                    });
                }

                // Initialize forms loading
                loadForms();

                // Load Q-Views on initialization
                loadQViews();

                // Load beautify library
                var beautifyScript = document.createElement('script ');
                beautifyScript.src = ' https ://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.7/beautify-html.min.js';
                document.head.appendChild(beautifyScript);
                var beautifyScript2 = document.createElement('script ');
                beautifyScript2.src = ' https ://cdnjs.cloudflare.com/ajax/libs/js-beautify/1.14.7/beautify.min.js';
                document.head.appendChild(beautifyScript2);
            });
        })(); // Close IIFE
    </script>
    <?php
}
add_action('admin_footer', 'qeditor_add_ace_editor');

/**
 * Ajax handler for rendering shortcodes in live preview with entry context.
 */
function qeditor_render_live_preview()
{
    if (!current_user_can('edit_posts')) {
        wp_send_json_error('Unauthorized');
    }

    // Verify nonce if provided
    if (isset($_POST['nonce']) && !wp_verify_nonce($_POST['nonce'], 'qeditor_nonce')) {
        wp_send_json_error('Invalid nonce');
    }

    if (!isset($_POST['content'])) {
        wp_send_json_error('No content');
        return;
    }

    $content = wp_unslash($_POST['content']);
    $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;
    $entry_id = isset($_POST['entry_id']) ? sanitize_text_field($_POST['entry_id']) : '';

    // Set up entry context for Formidable Forms shortcodes
    if ($form_id && $entry_id && class_exists('FrmEntry')) {
        if ($entry_id === 'sample') {
            // Use sample data
            qeditor_setup_sample_data($form_id);
        } else {
            // Use real entry data
            $entry = FrmEntry::getOne($entry_id);
            if ($entry && $entry->form_id == $form_id) {
                // Set global entry context for shortcode processing
                global $frm_vars;
                if (!isset($frm_vars)) {
                    $frm_vars = array();
                }
                $frm_vars['entry_id'] = $entry_id;
                $frm_vars['form_id'] = $form_id;
            }
        }
    }

    $processed = do_shortcode($content);
    wp_send_json_success($processed);
}
add_action('wp_ajax_qeditor_render_preview', 'qeditor_render_live_preview');

/**
 * Ajax handler for getting available forms.
 */
function qeditor_get_forms()
{
    if (!current_user_can('edit_posts')) {
        wp_send_json_error('Unauthorized');
    }

    $forms = array();

    if (class_exists('FrmForm')) {
        $frm_forms = FrmForm::get_published_forms();
        foreach ($frm_forms as $form) {
            $forms[$form->id] = $form->name . ' (ID: ' . $form->id . ')';
        }
    }

    wp_send_json_success($forms);
}
add_action('wp_ajax_qeditor_get_forms', 'qeditor_get_forms');

/**
 * Ajax handler for getting entries for a specific form.
 */
function qeditor_get_entries()
{
    if (!current_user_can('edit_posts')) {
        wp_send_json_error('Unauthorized');
    }

    $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;
    if (!$form_id) {
        wp_send_json_error('No form ID provided');
    }

    $entries = array();

    if (class_exists('FrmEntry')) {
        $frm_entries = FrmEntry::getAll(array('form_id' => $form_id), ' ORDER BY created_at DESC LIMIT 20');
        foreach ($frm_entries as $entry) {
            $title = 'Entry #' . $entry->id;
            if (!empty($entry->name)) {
                $title .= ' - ' . $entry->name;
            }
            $title .= ' (' . date('M j, Y', strtotime($entry->created_at)) . ')';
            $entries[$entry->id] = $title;
        }
    }

    wp_send_json_success($entries);
}
add_action('wp_ajax_qeditor_get_entries', 'qeditor_get_entries');

/**
 * Ajax handler for getting Q-Views.
 */
function qeditor_get_qviews()
{
    if (!current_user_can('edit_posts')) {
        wp_send_json_error('Unauthorized');
    }

    $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;
    $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;

    $args = array(
        'post_type' => 'q-view',
        'post_status' => 'publish',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC'
    );

    // Add taxonomy query to filter by category if specified
    if ($category_id > 0) {
        $args['tax_query'] = array(
            array(
                'taxonomy' => 'q-view-category',
                'field' => 'term_id',
                'terms' => $category_id,
            ),
        );
    }

    // Add meta query to filter by form if specified
    if ($form_id) {
        $args['meta_query'] = array(
            'relation' => 'OR',
            array(
                'key' => '_qview_associated_form',
                'value' => $form_id,
                'compare' => '='
            ),
            array(
                'key' => '_qview_type',
                'value' => 'general',
                'compare' => '='
            ),
            array(
                'key' => '_qview_type',
                'value' => 'template',
                'compare' => '='
            ),
            array(
                'key' => '_qview_associated_form',
                'compare' => 'NOT EXISTS'
            )
        );
    }

    $qviews = get_posts($args);
    $qview_list = array();
    $templates = array();
    $user_views = array();

    foreach ($qviews as $qview) {
        $is_template = get_post_meta($qview->ID, '_qview_is_template', true);
        $template_type = get_post_meta($qview->ID, '_qview_template_type', true);

        // Get category information
        $categories = wp_get_post_terms($qview->ID, 'q-view-category');
        $category_name = '';
        if (!is_wp_error($categories) && !empty($categories)) {
            $category_name = ' [' . $categories[0]->name . ']';
        }

        if ($is_template === '1') {
            // Add template indicator to title
            $title = $qview->post_title . ' [Template]';
            if ($template_type) {
                $title = $qview->post_title . ' [' . ucfirst($template_type) . ' Template]';
            }
            $title .= $category_name;
            $templates[$qview->ID] = $title;
        } else {
            $title = $qview->post_title . $category_name;
            $user_views[$qview->ID] = $title;
        }
    }

    // Combine templates first, then user views
    $qview_list = $templates + $user_views;

    wp_send_json_success($qview_list);
}
add_action('wp_ajax_qeditor_get_qviews', 'qeditor_get_qviews');

/**
 * Ajax handler for getting Q-View content.
 */
function qeditor_get_qview_content()
{
    if (!current_user_can('edit_posts')) {
        wp_send_json_error('Unauthorized');
    }

    $qview_id = isset($_POST['qview_id']) ? intval($_POST['qview_id']) : 0;
    if (!$qview_id) {
        wp_send_json_error('No Q-View ID provided');
    }

    $qview = get_post($qview_id);
    if (!$qview || $qview->post_type !== 'q-view') {
        wp_send_json_error('Q-View not found');
    }

    wp_send_json_success($qview->post_content);
}
add_action('wp_ajax_qeditor_get_qview_content', 'qeditor_get_qview_content');

/**
 * Ajax handler for saving Q-View.
 */
function qeditor_save_qview()
{
    if (!current_user_can('edit_posts')) {
        wp_send_json_error('Unauthorized');
    }

    $title = isset($_POST['title']) ? sanitize_text_field($_POST['title']) : '';
    $content = isset($_POST['content']) ? wp_unslash($_POST['content']) : '';
    $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;

    if (empty($title)) {
        wp_send_json_error('Title is required');
    }

    $post_data = array(
        'post_title' => $title,
        'post_content' => $content,
        'post_type' => 'q-view',
        'post_status' => 'publish'
    );

    $qview_id = wp_insert_post($post_data);

    if (is_wp_error($qview_id)) {
        wp_send_json_error('Failed to save Q-View: ' . $qview_id->get_error_message());
    }

    // Save meta data
    if ($form_id) {
        update_post_meta($qview_id, '_qview_associated_form', $form_id);
        update_post_meta($qview_id, '_qview_type', 'form-specific');
    } else {
        update_post_meta($qview_id, '_qview_type', 'general');
    }

    wp_send_json_success(array('id' => $qview_id, 'message' => 'Q-View saved successfully'));
}
add_action('wp_ajax_qeditor_save_qview', 'qeditor_save_qview');

/**
 * Create default Q-View templates on plugin activation.
 */
function qeditor_create_default_qview_templates()
{
    // Check if templates already exist
    $existing_templates = get_posts(array(
        'post_type' => 'q-view',
        'meta_query' => array(
            array(
                'key' => '_qview_is_template',
                'value' => '1',
                'compare' => '='
            )
        ),
        'posts_per_page' => -1
    ));

    // If templates already exist, don't create duplicates
    if (!empty($existing_templates)) {
        return;
    }

    $templates = qeditor_get_default_template_definitions();

    foreach ($templates as $template) {
        $post_data = array(
            'post_title' => $template['title'],
            'post_content' => $template['content'],
            'post_type' => 'q-view',
            'post_status' => 'publish',
            'post_author' => 1 // Admin user
        );

        $qview_id = wp_insert_post($post_data);

        if (!is_wp_error($qview_id)) {
            // Mark as template
            update_post_meta($qview_id, '_qview_is_template', '1');
            update_post_meta($qview_id, '_qview_type', 'template');
            update_post_meta($qview_id, '_qview_template_type', $template['type']);
        }
    }
}

/**
 * Get default template definitions.
 */
function qeditor_get_default_template_definitions()
{
    return array(
        array(
            'title' => 'Table View Template',
            'type' => 'table',
            'content' => '<div class="qview-table-container">
    <style>
        .qview-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-family: Arial, sans-serif;
        }
        .qview-table th,
        .qview-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .qview-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .qview-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .qview-table tr:hover {
            background-color: #f5f5f5;
        }
    </style>

    <table class="qview-table">
        <thead>
            <tr>
                <th>Field Name</th>
                <th>Value</th>
                <th>Date</th>
            </tr>
        </thead>
        <tbody>
            [frm-entries form_id="YOUR_FORM_ID" limit="10"]
            <tr>
                <td>[frm-field id="FIELD_ID_1"]</td>
                <td>[frm-field id="FIELD_ID_2"]</td>
                <td>[frm-field id="created_date" format="M j, Y"]</td>
            </tr>
            [/frm-entries]
        </tbody>
    </table>
</div>'
        ),
        array(
            'title' => 'Accordion View Template',
            'type' => 'accordion',
            'content' => '<div class="qview-accordion-container">
    <style>
        .qview-accordion {
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .qview-accordion-item {
            border-bottom: 1px solid #ddd;
        }
        .qview-accordion-item:last-child {
            border-bottom: none;
        }
        .qview-accordion-header {
            background-color: #f8f9fa;
            padding: 15px;
            cursor: pointer;
            font-weight: bold;
            position: relative;
        }
        .qview-accordion-header:hover {
            background-color: #e9ecef;
        }
        .qview-accordion-header::after {
            content: "+";
            position: absolute;
            right: 15px;
            font-size: 18px;
        }
        .qview-accordion-header.active::after {
            content: "-";
        }
        .qview-accordion-content {
            padding: 15px;
            display: none;
            background-color: #fff;
        }
        .qview-accordion-content.active {
            display: block;
        }
    </style>

    <div class="qview-accordion">
        [frm-entries form_id="YOUR_FORM_ID" limit="5"]
        <div class="qview-accordion-item">
            <div class="qview-accordion-header" onclick="toggleAccordion(this)">
                Entry #[frm-field id="id"] - [frm-field id="FIELD_ID_1"]
            </div>
            <div class="qview-accordion-content">
                <p><strong>Field 1:</strong> [frm-field id="FIELD_ID_1"]</p>
                <p><strong>Field 2:</strong> [frm-field id="FIELD_ID_2"]</p>
                <p><strong>Date:</strong> [frm-field id="created_date" format="M j, Y g:i A"]</p>
            </div>
        </div>
        [/frm-entries]
    </div>

    <script>
        function toggleAccordion(element) {
            element.classList.toggle("active");
            var content = element.nextElementSibling;
            content.classList.toggle("active");
        }
    </script>
</div>'
        ),
        array(
            'title' => 'Card View Template',
            'type' => 'card',
            'content' => '<div class="qview-cards-container">
    <style>
        .qview-cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .qview-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: box-shadow 0.3s ease;
        }
        .qview-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .qview-card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .qview-card-content {
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .qview-card-meta {
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
    </style>

    <div class="qview-cards-grid">
        [frm-entries form_id="YOUR_FORM_ID" limit="6"]
        <div class="qview-card">
            <div class="qview-card-title">[frm-field id="FIELD_ID_1"]</div>
            <div class="qview-card-content">
                <p>[frm-field id="FIELD_ID_2"]</p>
            </div>
            <div class="qview-card-meta">
                Entry #[frm-field id="id"] | [frm-field id="created_date" format="M j, Y"]
            </div>
        </div>
        [/frm-entries]
    </div>
</div>'
        ),
        array(
            'title' => 'Alert View Template',
            'type' => 'alert',
            'content' => '<div class="qview-alerts-container">
    <style>
        .qview-alert {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
            font-family: Arial, sans-serif;
        }
        .qview-alert-success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .qview-alert-info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .qview-alert-warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .qview-alert-danger {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .qview-alert-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .qview-alert-meta {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 10px;
        }
    </style>

    [frm-entries form_id="YOUR_FORM_ID" limit="5"]
    <div class="qview-alert qview-alert-info">
        <div class="qview-alert-title">[frm-field id="FIELD_ID_1"]</div>
        <div>[frm-field id="FIELD_ID_2"]</div>
        <div class="qview-alert-meta">
            Entry #[frm-field id="id"] | [frm-field id="created_date" format="M j, Y g:i A"]
        </div>
    </div>
    [/frm-entries]
</div>'
        ),
        array(
            'title' => 'List View Template',
            'type' => 'list',
            'content' => '<div class="qview-list-container">
    <style>
        .qview-list {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }
        .qview-list-item {
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fff;
            transition: background-color 0.3s ease;
        }
        .qview-list-item:hover {
            background-color: #f8f9fa;
        }
        .qview-list-item-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 8px;
            color: #333;
        }
        .qview-list-item-content {
            margin-bottom: 10px;
            line-height: 1.4;
        }
        .qview-list-item-meta {
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 8px;
        }
        .qview-list-item-badge {
            display: inline-block;
            padding: 2px 8px;
            background-color: #007cba;
            color: white;
            border-radius: 3px;
            font-size: 11px;
            margin-right: 5px;
        }
    </style>

    <ul class="qview-list">
        [frm-entries form_id="YOUR_FORM_ID" limit="10"]
        <li class="qview-list-item">
            <div class="qview-list-item-title">
                <span class="qview-list-item-badge">#[frm-field id="id"]</span>
                [frm-field id="FIELD_ID_1"]
            </div>
            <div class="qview-list-item-content">
                [frm-field id="FIELD_ID_2"]
            </div>
            <div class="qview-list-item-meta">
                Created: [frm-field id="created_date" format="M j, Y g:i A"]
            </div>
        </li>
        [/frm-entries]
    </ul>
</div>'
        )
    );
}

/**
 * Create default Q-View categories.
 */
function qeditor_create_default_qview_categories()
{
    // Check if categories already exist
    $existing_categories = get_terms(array(
        'taxonomy' => 'q-view-category',
        'hide_empty' => false,
    ));

    // If categories already exist, don't create duplicates
    if (!is_wp_error($existing_categories) && !empty($existing_categories)) {
        return;
    }

    $default_categories = array(
        'Templates' => 'Pre-built view templates for common layouts',
        'Data Display' => 'Views for displaying form data and entries',
        'Reports' => 'Views for generating reports and analytics',
        'User Interface' => 'Views for user-facing interfaces and dashboards',
        'Custom' => 'Custom views for specific use cases'
    );

    foreach ($default_categories as $name => $description) {
        $term = wp_insert_term(
            $name,
            'q-view-category',
            array(
                'description' => $description,
                'slug' => sanitize_title($name)
            )
        );

        // If this is the Templates category, assign existing templates to it
        if (!is_wp_error($term) && $name === 'Templates') {
            $templates = get_posts(array(
                'post_type' => 'q-view',
                'meta_query' => array(
                    array(
                        'key' => '_qview_is_template',
                        'value' => '1',
                        'compare' => '='
                    )
                ),
                'posts_per_page' => -1
            ));

            foreach ($templates as $template) {
                wp_set_post_terms($template->ID, array($term['term_id']), 'q-view-category');
            }
        }
    }
}

/**
 * Plugin activation hook to create default templates.
 */
function qeditor_plugin_activation()
{
    // Register the post type and taxonomy first
    qeditor_register_qview_post_type();
    qeditor_register_qview_category_taxonomy();

    // Flush rewrite rules
    flush_rewrite_rules();

    // Create default categories
    qeditor_create_default_qview_categories();

    // Create default templates
    qeditor_create_default_qview_templates();
}
register_activation_hook(__FILE__, 'qeditor_plugin_activation');

/**
 * Ensure default templates exist (run on init as backup).
 */
function qeditor_ensure_default_templates()
{
    // Only run this occasionally to avoid performance issues
    $last_check = get_option('qeditor_last_template_check', 0);
    $current_time = time();

    // Check once per day
    if (($current_time - $last_check) > DAY_IN_SECONDS) {
        qeditor_create_default_qview_templates();
        update_option('qeditor_last_template_check', $current_time);
    }
}
add_action('init', 'qeditor_ensure_default_templates', 20);

/**
 * Manual function to create templates (for testing/debugging).
 * Can be called from WordPress admin or via URL parameter.
 */
function qeditor_create_templates_manually()
{
    if (current_user_can('manage_options') && isset($_GET['qeditor_create_templates'])) {
        qeditor_create_default_qview_templates();
        wp_die('Q-View templates created successfully! <a href="' . admin_url('edit.php?post_type=q-view') . '">View Q-Views</a>');
    }
}
add_action('init', 'qeditor_create_templates_manually');

/**
 * Set up sample data for preview when using "sample" entry option.
 */
function qeditor_setup_sample_data($form_id)
{
    if (!class_exists('FrmField') || !class_exists('FrmProEntriesController')) {
        return;
    }

    // Get form fields
    $fields = FrmField::get_all_for_form($form_id);

    // Create sample data based on field types
    $sample_data = array();
    foreach ($fields as $field) {
        $sample_value = qeditor_get_sample_field_value($field);
        if ($sample_value !== null) {
            $sample_data[$field->id] = $sample_value;
        }
    }

    // Set up global variables for Formidable shortcode processing
    global $frm_vars;
    if (!isset($frm_vars)) {
        $frm_vars = array();
    }
    $frm_vars['sample_data'] = $sample_data;
    $frm_vars['form_id'] = $form_id;
    $frm_vars['entry_id'] = 'sample';

    // Hook into Formidable's field value retrieval to provide sample data
    add_filter('frm_get_field_value_shortcode', 'qeditor_filter_field_value_for_sample', 10, 2);
}

/**
 * Generate sample value based on field type.
 */
function qeditor_get_sample_field_value($field)
{
    switch ($field->type) {
        case 'text':
        case 'textarea':
            return 'Sample ' . ucfirst($field->type) . ' Value';
        case 'email':
            return '<EMAIL>';
        case 'url':
            return 'https://example.com';
        case 'phone':
            return '(*************';
        case 'number':
            return '42';
        case 'date':
            return date('Y-m-d');
        case 'time':
            return date('H:i');
        case 'select':
        case 'radio':
        case 'checkbox':
            // Get first option if available
            if (!empty($field->options)) {
                $options = maybe_unserialize($field->options);
                if (is_array($options) && !empty($options)) {
                    return array_keys($options)[0];
                }
            }
            return 'Sample Option';
        case 'user_id':
            return get_current_user_id();
        default:
            return 'Sample Value';
    }
}

/**
 * Filter field values to provide sample data when in sample mode.
 */
function qeditor_filter_field_value_for_sample($value, $atts)
{
    global $frm_vars;

    if (isset($frm_vars['entry_id']) && $frm_vars['entry_id'] === 'sample' && isset($frm_vars['sample_data'])) {
        $field_id = isset($atts['field_id']) ? $atts['field_id'] : '';
        if ($field_id && isset($frm_vars['sample_data'][$field_id])) {
            return $frm_vars['sample_data'][$field_id];
        }
    }

    return $value;
}
